# =======================================LEVEL 5======================================
# Emotion detection
import cv2
import mediapipe as mp
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
from datetime import datetime
import pygame
import os
from fpdf import FPDF
from collections import Counter
import time
import random

st.set_page_config(page_title="Advanced Proctoring System", layout="wide")

st.markdown(
    """
    <style>
    .block-container {
        background-color: #0e1117;
        color: white;
    }
    .stMarkdown h1, .stMarkdown h2, .stMarkdown h3 {
        color: white;
    }
    .stDataFrame, .stButton, .stPlotlyChart {
        background-color: #0e1117 !important;
    }
    </style>
    """,
    unsafe_allow_html=True
)

st.title("🎓 Advanced Proctoring System")
st.markdown("Tracks **attention**, **distraction**, **phone usage**, and **posture** in real-time.")

run = st.toggle("Start Proctoring")

frame_placeholder = st.empty()
status_placeholder = st.empty()
graph_placeholder = st.container()
pie_placeholder = st.container()

# MediaPipe setup
mp_face = mp.solutions.face_detection
mp_pose = mp.solutions.pose
mp_hands = mp.solutions.hands

face_detection = mp_face.FaceDetection(min_detection_confidence=0.5)
pose = mp_pose.Pose()
hands = mp_hands.Hands()

log_data = []
emotion_log = []
position_log = []
attention_scores = []
drowsiness_counter = 0

cap = cv2.VideoCapture(0)

# Pygame alert sound setup
pygame.mixer.init()
alert_sound = None
alert_playing = False
if os.path.exists("./assets/alert.mp3"):
    try:
        alert_sound = pygame.mixer.Sound("./assets/alert.mp3")
    except Exception as e:
        st.warning(f"\u26a0\ufe0f Could not load alert sound: {e}")
else:
    st.warning("\u26a0\ufe0f 'alert.mp3' not found in './assets'. Alerts will be silent.")

# Dummy emotion classifier
emotions = ["Neutral", "Happy", "Sad", "Angry"]
def detect_emotion():
    return random.choice(emotions)

def detect_status(frame):
    global drowsiness_counter
    status = "Attentive"
    rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    face_results = face_detection.process(rgb)
    pose_results = pose.process(rgb)
    hand_results = hands.process(rgb)
    h, w, _ = frame.shape

    face_present = face_results.detections is not None
    if not face_present:
        status = "No Face / Distraction"
        return status, (0, 0)

    face_box = (0, 0)
    if face_present:
        for detection in face_results.detections:
            bboxC = detection.location_data.relative_bounding_box
            x = int(bboxC.xmin * w)
            y = int(bboxC.ymin * h)
            face_box = (x, y)
            cv2.rectangle(frame, (x, y), (x+50, y+50), (0, 255, 0), 2)

    if pose_results.pose_landmarks:
        landmarks = pose_results.pose_landmarks.landmark
        left_shoulder = landmarks[mp_pose.PoseLandmark.LEFT_SHOULDER.value]
        right_shoulder = landmarks[mp_pose.PoseLandmark.RIGHT_SHOULDER.value]
        shoulder_dist = abs(left_shoulder.x - right_shoulder.x)
        if shoulder_dist < 0.1:
            status = "Leaning / Bad Posture"

    if hand_results.multi_hand_landmarks:
        status = "Phone Usage / Hands Detected"

    # Blink/Drowsiness detection (simulated)
    if random.random() < 0.1:
        drowsiness_counter += 1
        if drowsiness_counter >= 5:
            status = "Drowsy / Eyes Closed"
    else:
        drowsiness_counter = max(0, drowsiness_counter - 1)

    return status, face_box

# Main loop
while run:
    ret, frame = cap.read()
    if not ret:
        break

    frame = cv2.flip(frame, 1)
    status, face_pos = detect_status(frame)
    emotion = detect_emotion()

    # Engagement Score (simplified logic)
    score = 100
    if status != "Attentive":
        score -= 40
    if emotion in ["Sad", "Angry"]:
        score -= 30
    score = max(0, min(100, score))
    attention_scores.append(score)

    # Play alert sound
    if status != "Attentive":
        if alert_sound and not alert_playing:
            alert_sound.play(-1)
            alert_playing = True
    else:
        if alert_sound and alert_playing:
            alert_sound.stop()
            alert_playing = False

    timestamp = datetime.now().strftime("%H:%M:%S")
    log_data.append({"Time": timestamp, "Status": status})
    emotion_log.append({"Time": timestamp, "Emotion": emotion})
    position_log.append(face_pos)

    cv2.putText(frame, f'Status: {status}', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
    cv2.putText(frame, f'Emotion: {emotion}', (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 255), 2)
    cv2.putText(frame, f'Engagement Score: {score}', (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    frame_placeholder.image(frame_rgb, channels="RGB")
    status_placeholder.markdown(f"### 🟢 Current Status: **{status}** | Emotion: **{emotion}** | Score: **{score}%**")

cap.release()

if not run and log_data:
    log_df = pd.DataFrame(log_data)
    emotion_df = pd.DataFrame(emotion_log)
    st.subheader("📊 Proctoring Report")

    # Focus Timeline
    fig = px.line(log_df, x="Time", y="Status", title="Focus Timeline", template="plotly_dark")
    graph_placeholder.plotly_chart(fig, use_container_width=True)

    # Emotion Chart
    st.markdown("### 😊 Real-Time Emotion Chart")
    emotion_fig = px.line(emotion_df, x="Time", y="Emotion", title="Emotion Trend", template="plotly_dark")
    st.plotly_chart(emotion_fig, use_container_width=True)

    # Engagement Pie
    st.markdown("### 📈 Engagement Score Distribution")
    pie_fig = px.histogram(attention_scores, nbins=10, template="plotly_dark")
    st.plotly_chart(pie_fig, use_container_width=True)

    # Posture Heatmap
    st.markdown("### 🔥 Face Position Heatmap")
    heatmap = np.zeros((480, 640), dtype=np.uint8)
    for (x, y) in position_log:
        x = min(max(x, 0), 639)
        y = min(max(y, 0), 479)
        heatmap[y, x] = min(255, heatmap[y, x] + 10)
    st.image(heatmap, clamp=True, channels="GRAY")

    # Session Rating
    final_score = int(np.mean(attention_scores))
    rating = min(5, max(1, final_score // 20))
    st.markdown(f"### ⭐ Session Rating: {rating}/5")

    # Distraction Cause (simple logic)
    causes = [d["Status"] for d in log_data if d["Status"] != "Attentive"]
    cause_counts = Counter(causes)
    if cause_counts:
        top_cause = cause_counts.most_common(1)[0][0]
        st.markdown(f"### 🧠 Most Frequent Distraction Cause: **{top_cause}**")

    # Export buttons
    st.download_button("⬇️ Download Log as CSV", data=log_df.to_csv(index=False), file_name="proctoring_log.csv")
