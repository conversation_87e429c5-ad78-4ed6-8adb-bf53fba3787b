import streamlit as st
import hashlib
import os
from pathlib import Path
import subprocess
import sys
from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError

# Page config will be set by the launcher script

# Database configuration
DATABASE_URL = "sqlite:///users.db"
Base = declarative_base()

# Database Models
class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True, autoincrement=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    name = Column(String(255), nullable=False)
    role = Column(String(50), nullable=False, default='student')
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)

    def __repr__(self):
        return f"<User(email='{self.email}', name='{self.name}', role='{self.role}')>"


# Additional models are defined in database_models.py to avoid conflicts

# Database setup
engine = create_engine(DATABASE_URL, echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def init_database():
    """Initialize database and create tables"""
    try:
        # Create tables for main app
        Base.metadata.create_all(bind=engine)

        # Import and create tables for enhanced models
        try:
            import database_models
            database_models.Base.metadata.create_all(bind=database_models.engine)
        except Exception as e:
            print(f"Warning: Could not initialize enhanced database models: {e}")

        return True
    except Exception as e:
        print(f"Error initializing database: {e}")
        return False

def get_db_session():
    """Get database session"""
    return SessionLocal()

def hash_password(password):
    """Hash password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, hashed_password):
    """Verify password against hash"""
    return hash_password(password) == hashed_password

def create_default_users():
    """Create default users if none exist"""
    db = get_db_session()
    try:
        # Check if any users exist
        user_count = db.query(User).count()
        if user_count == 0:
            # Create default admin user
            admin_user = User(
                email="<EMAIL>",
                password_hash=hash_password("admin123"),
                name="Administrator",
                role="admin"
            )

            # Create default student user
            student_user = User(
                email="<EMAIL>",
                password_hash=hash_password("student123"),
                name="Test Student",
                role="student"
            )

            db.add(admin_user)
            db.add(student_user)
            db.commit()

            return True
        return False
    except Exception as e:
        db.rollback()
        print(f"Error creating default users: {e}")
        return False
    finally:
        db.close()

def authenticate_user(email, password):
    """Authenticate user with email and password"""
    db = get_db_session()
    try:
        user = db.query(User).filter(User.email == email, User.is_active == True).first()
        if user and verify_password(password, user.password_hash):
            # Update last login time
            user.last_login = datetime.utcnow() # pyright: ignore[reportAttributeAccessIssue]
            db.commit()

            return {
                "id": user.id,
                "email": user.email,
                "name": user.name,
                "role": user.role,
                "last_login": user.last_login
            }
        return None
    except Exception as e:
        print(f"Error authenticating user: {e}")
        return None
    finally:
        db.close()

def register_user(email, password, name, role="student"):
    """Register a new user"""
    db = get_db_session()
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == email).first()
        if existing_user:
            return False, "Email already exists"

        # Create new user
        new_user = User(
            email=email,
            password_hash=hash_password(password),
            name=name,
            role=role
        )

        db.add(new_user)
        db.commit()
        return True, "User registered successfully"

    except IntegrityError:
        db.rollback()
        return False, "Email already exists"
    except Exception as e:
        db.rollback()
        print(f"Error registering user: {e}")
        return False, f"Registration failed: {str(e)}"
    finally:
        db.close()

def get_user_by_email(email):
    """Get user by email"""
    db = get_db_session()
    try:
        user = db.query(User).filter(User.email == email, User.is_active == True).first()
        if user:
            return {
                "id": user.id,
                "email": user.email,
                "name": user.name,
                "role": user.role,
                "created_at": user.created_at,
                "last_login": user.last_login
            }
        return None
    except Exception as e:
        print(f"Error getting user: {e}")
        return None
    finally:
        db.close()

def get_all_users():
    """Get all active users (for admin purposes)"""
    db = get_db_session()
    try:
        users = db.query(User).filter(User.is_active == True).all()
        return [{
            "id": user.id,
            "email": user.email,
            "name": user.name,
            "role": user.role,
            "created_at": user.created_at,
            "last_login": user.last_login
        } for user in users]
    except Exception as e:
        print(f"Error getting users: {e}")
        return []
    finally:
        db.close()

def login_page():
    """Display login page with auto-login and registration"""

    # Add dark theme CSS
    st.markdown("""
    <style>
    .info-card {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        color: white;
        margin: 1rem 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    /* Global white text styling */
    h3, h4, p {
        color: #FFFFFF !important;
    }

    .stMarkdown h3, .stMarkdown h4, .stMarkdown p {
        color: #FFFFFF !important;
    }

    .info-card h3, .info-card h4, .info-card p, .info-card li {
        color: #FFFFFF !important;
    }

    .stForm {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        padding: 1.5rem;
        border-radius: 10px;
        border: 1px solid #667eea;
        color: white;
    }

    .stForm .stMarkdown, .stForm h3, .stForm h4, .stForm p {
        color: #FFFFFF !important;
    }

    .stExpander {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        border-radius: 10px;
        border: 1px solid #667eea;
    }

    /* Style form inputs for dark theme */
    .stForm .stTextInput > div > div > input {
        background-color: #34495e;
        color: white;
        border: 1px solid #667eea;
    }

    .stForm .stSelectbox > div > div > div {
        background-color: #34495e;
        color: white;
        border: 1px solid #667eea;
    }
    </style>
    """, unsafe_allow_html=True)

    st.title("🔐 AI Examiner")
    st.markdown("**Advanced AI-Powered Online Examination System**")
    st.markdown("---")

    # Check for auto-login (browser session)
    if 'auto_login_checked' not in st.session_state:
        st.session_state.auto_login_checked = True
        # Try to auto-login with default credentials for demo
        if 'demo_auto_login' not in st.session_state:
            st.session_state.demo_auto_login = True
            # You can remove this in production
            st.info("🔄 Checking for saved login...")

    # Show login/register options
    col1, col2 = st.columns(2)

    with col1:
        if st.button("🔐 Login", type="primary", use_container_width=True):
            st.session_state.show_login = True
            st.session_state.show_register = False

    with col2:
        if st.button("📝 Register New Account", type="secondary", use_container_width=True):
            st.session_state.show_register = True
            st.session_state.show_login = False

    # Show login form
    if st.session_state.get('show_login', True):
        st.markdown("---")
        st.subheader("🔐 Login to your account")

        with st.form("login_form"):
            email = st.text_input("Email", placeholder="Enter your email")
            password = st.text_input("Password", type="password", placeholder="Enter your password")
            remember_me = st.checkbox("Remember me on this browser")
            submit_button = st.form_submit_button("Login", type="primary")

            if submit_button:
                if email and password:
                    user = authenticate_user(email, password)
                    if user:
                        # Store user info in session state
                        st.session_state.authenticated = True
                        st.session_state.user_email = email
                        st.session_state.user_name = user["name"]
                        st.session_state.user_role = user["role"]

                        if remember_me:
                            st.session_state.remember_login = True

                        st.success(f"Welcome back, {user['name']}!")
                        st.rerun()
                    else:
                        st.error("Invalid email or password")
                else:
                    st.error("Please enter both email and password")

    # Show registration form
    if st.session_state.get('show_register', False):
        st.markdown("---")
        st.subheader("📝 Create a new account")

        with st.form("register_form"):
            reg_name = st.text_input("Full Name", placeholder="Enter your full name")
            reg_email = st.text_input("Email", placeholder="Enter your email")
            reg_password = st.text_input("Password", type="password", placeholder="Enter your password")
            reg_confirm_password = st.text_input("Confirm Password", type="password", placeholder="Confirm your password")
            reg_role = st.selectbox("Account Type", ["student", "admin"], help="Select your account type")
            register_button = st.form_submit_button("Register", type="primary")

            if register_button:
                if reg_name and reg_email and reg_password and reg_confirm_password:
                    if reg_password == reg_confirm_password:
                        if len(reg_password) >= 6:
                            success, message = register_user(reg_email, reg_password, reg_name, reg_role)
                            if success:
                                st.success(message)
                                st.info("Registration successful! You can now login with your new account.")
                                st.session_state.show_register = False
                                st.session_state.show_login = True
                                st.rerun()
                            else:
                                st.error(message)
                        else:
                            st.error("Password must be at least 6 characters long")
                    else:
                        st.error("Passwords do not match")
                else:
                    st.error("Please fill in all fields")

    # Instructions and rules
    st.markdown("---")
    st.markdown("### 📋 System Instructions & Rules")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown('<div class="info-card">', unsafe_allow_html=True)
        st.markdown("""
        **🎯 For Students:**
        - **Login** with your registered credentials
        - **Camera & Microphone** access is required for all tests
        - **Stay focused** - avoid switching tabs or applications
        - **Maintain visibility** - keep your face visible to the camera
        - **No external help** - tests are monitored for academic integrity
        - **Submit on time** - tests auto-submit when time expires
        """)
        st.markdown('</div>', unsafe_allow_html=True)

    with col2:
        st.markdown('<div class="info-card">', unsafe_allow_html=True)
        st.markdown("""
        **🔧 For Administrators:**
        - **Create tests** by uploading PDF documents
        - **Manage sessions** with time limits and participant controls
        - **Monitor students** through real-time proctoring
        - **Review results** with automatic and manual grading
        - **Access recordings** for detailed analysis
        - **Generate reports** for academic integrity assessment
        """)
        st.markdown('</div>', unsafe_allow_html=True)

    st.markdown("---")
    st.markdown('<div class="info-card">', unsafe_allow_html=True)
    st.markdown("### ⚠️ Important Rules")
    st.markdown("""
    **Academic Integrity Policy:**
    - All test sessions are recorded (audio + video)
    - AI monitoring detects suspicious behavior
    - Multiple violations may result in test termination
    - Cheating attempts are logged and reported
    - Maintain a quiet, well-lit testing environment
    """)
    st.markdown('</div>', unsafe_allow_html=True)

    with st.expander("🔑 Default Login Credentials (Demo)"):
        st.markdown('<div class="info-card">', unsafe_allow_html=True)
        st.markdown("""
        **Admin Account:**
        - Email: <EMAIL>
        - Password: admin123

        **Student Account:**
        - Email: <EMAIL>
        - Password: student123
        """)
        st.markdown('</div>', unsafe_allow_html=True)

def main_dashboard():
    """Display main dashboard after login"""
    st.title(f"🎓 Welcome, {st.session_state.user_name}!")
    
    # User info sidebar
    with st.sidebar:
        st.markdown("### User Information")
        st.write(f"**Name:** {st.session_state.user_name}")
        st.write(f"**Email:** {st.session_state.user_email}")
        st.write(f"**Role:** {st.session_state.user_role.title()}")
        
        st.markdown("---")
        if st.button("Logout", type="secondary"):
            # Clear session state
            for key in list(st.session_state.keys()):
                del st.session_state[key]
            st.rerun()
    
    st.markdown("---")
    
    # Main content based on user role
    if st.session_state.user_role == 'admin':
        # Admin interface
        st.subheader("🔧 Admin Dashboard")

        # Admin tabs
        tab1, tab2, tab3 = st.tabs(["Test Management", "System Control", "Reports"])

        with tab1:
            # Import and show test management
            try:
                from test_management import show_test_management
                show_test_management()
            except ImportError as e:
                st.error(f"Error loading test management: {e}")

        with tab2:
            st.subheader("🚀 System Control")

            col1, col2 = st.columns([2, 1])

            with col1:
                st.markdown("""
                **Proctoring System Control**

                Launch the background proctoring system that will monitor students during tests.
                """)

                col_a, col_b = st.columns(2)

                with col_a:
                    if st.button("🎥 Launch Proctoring System", type="primary"):
                        try:
                            import subprocess
                            import sys

                            # Launch proctor_app.py on a different port
                            subprocess.Popen([
                                sys.executable, "-m", "streamlit", "run",
                                "proctor_app.py", "--server.port", "8502"
                            ])

                            st.success("🎉 Proctoring system launched!")
                            st.info("📱 Access it at: http://localhost:8502")

                        except Exception as e:
                            st.error(f"Failed to launch proctoring system: {e}")
                            st.info("You can manually run: `streamlit run proctor_app.py --server.port 8502`")

                with col_b:
                    if st.button("📝 Launch Student Test Portal", type="secondary"):
                        try:
                            import subprocess
                            import sys

                            # Launch student_app.py on a different port
                            subprocess.Popen([
                                sys.executable, "-m", "streamlit", "run",
                                "student_app.py", "--server.port", "8503"
                            ])

                            st.success("🎉 Student test portal launched!")
                            st.info("📱 Students can access tests at: http://localhost:8503")

                        except Exception as e:
                            st.error(f"Failed to launch student portal: {e}")
                            st.info("You can manually run: `streamlit run student_app.py --server.port 8503`")

        with tab3:
            # Import and show admin dashboard
            try:
                from admin_dashboard import show_admin_dashboard
                show_admin_dashboard()
            except ImportError as e:
                st.error(f"Error loading admin dashboard: {e}")

    else:
        # Student interface
        col1, col2 = st.columns([2, 1])

        with col1:
            st.subheader("📚 Student Dashboard")
            st.info("Welcome! Your admin will provide you with test access when a proctoring session is started.")

            # Show available tests (if any are assigned)
            st.subheader("📝 Available Tests")
            st.info("No tests currently assigned. Please wait for your instructor to start a proctoring session.")
    
    with col2:
        st.subheader("📊 Quick Stats")

        # Get user statistics
        all_users = get_all_users()
        total_users = len(all_users)
        admin_users = len([u for u in all_users if u['role'] == 'admin'])
        student_users = len([u for u in all_users if u['role'] == 'student'])

        st.metric("Total Users", total_users)
        st.metric("Admin Users", admin_users)
        st.metric("Student Users", student_users)

        st.markdown("---")
        st.subheader("🔧 System Status")
        st.success("✅ Database Connected")
        st.success("✅ Camera Available")
        st.success("✅ AI Models Loaded")
        st.success("✅ System Ready")

    # Admin panel for viewing users
    if st.session_state.user_role == 'admin':
        st.markdown("---")
        st.subheader("👥 User Management (Admin Only)")

        with st.expander("View All Users", expanded=False):
            users = get_all_users()
            if users:
                import pandas as pd
                df = pd.DataFrame(users)
                # Format datetime columns
                if 'created_at' in df.columns:
                    df['created_at'] = pd.to_datetime(df['created_at']).dt.strftime('%Y-%m-%d %H:%M')
                if 'last_login' in df.columns:
                    df['last_login'] = pd.to_datetime(df['last_login']).dt.strftime('%Y-%m-%d %H:%M')

                st.dataframe(
                    df[['id', 'email', 'name', 'role', 'created_at', 'last_login']],
                    use_container_width=True,
                    hide_index=True
                )
            else:
                st.info("No users found in database.")

def main():
    """Main application function"""
    # Initialize database
    init_database()

    # Initialize session state
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False

    # Initialize login/register state
    if 'show_login' not in st.session_state:
        st.session_state.show_login = True
    if 'show_register' not in st.session_state:
        st.session_state.show_register = False

    # Create default users if none exist
    create_default_users()

    # Auto-login check (for demo purposes)
    if not st.session_state.authenticated and st.session_state.get('demo_auto_login', False):
        # Try to auto-login with demo credentials
        if 'auto_login_attempted' not in st.session_state:
            st.session_state.auto_login_attempted = True
            # This is just for demo - remove in production
            st.info("🔄 Demo mode: Auto-login available with default credentials")

    # Show appropriate page based on authentication status
    if st.session_state.authenticated:
        main_dashboard()
    else:
        login_page()

if __name__ == "__main__":
    main()
