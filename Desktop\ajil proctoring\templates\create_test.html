<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Test - Ajil Proctoring</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6f42c1;
            --secondary-color: #e9ecef;
            --accent-color: #f8f9fa;
            --text-color: #495057;
            --border-color: #dee2e6;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .test-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .card-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
        }

        .card-header h2 {
            margin: 0;
            font-weight: 600;
            font-size: 1.8rem;
        }

        .card-body {
            padding: 2.5rem;
        }

        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 15px;
            padding: 3rem 2rem;
            text-align: center;
            background: var(--accent-color);
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 2rem;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background: rgba(111, 66, 193, 0.05);
        }

        .upload-area.dragover {
            border-color: var(--primary-color);
            background: rgba(111, 66, 193, 0.1);
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .upload-text {
            color: var(--text-color);
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .upload-subtext {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 2rem;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.8rem;
            display: block;
        }

        .form-control {
            border: 2px solid var(--border-color);
            border-radius: 10px;
            padding: 0.8rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
        }

        .btn-primary {
            background: var(--primary-color);
            border: none;
            border-radius: 10px;
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: #5a359a;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(111, 66, 193, 0.3);
        }

        .btn-secondary {
            background: var(--secondary-color);
            border: none;
            border-radius: 10px;
            padding: 0.8rem 1.5rem;
            color: var(--text-color);
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #d1ecf1;
            color: #0c5460;
        }

        .file-info {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            display: none;
        }

        .file-info.show {
            display: block;
        }

        .progress-container {
            margin-top: 1rem;
            display: none;
        }

        .progress {
            height: 8px;
            border-radius: 10px;
            background: var(--secondary-color);
        }

        .progress-bar {
            background: var(--primary-color);
            border-radius: 10px;
        }

        .test-options {
            background: var(--accent-color);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .option-group {
            margin-bottom: 1.5rem;
        }

        .option-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }

        .form-check {
            margin-bottom: 0.5rem;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .time-selector {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .time-input {
            width: 100px;
        }

        .back-btn {
            position: absolute;
            left: 2rem;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 1rem auto;
                padding: 0 0.5rem;
            }
            
            .card-body {
                padding: 1.5rem;
            }
            
            .upload-area {
                padding: 2rem 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="test-card">
            <div class="card-header">
                <button class="back-btn" onclick="window.history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h2>Create New Test</h2>
            </div>
            
            <div class="card-body">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form id="testForm" method="POST" enctype="multipart/form-data">
                    <!-- File Upload Section -->
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">Upload your material here</div>
                        <div class="upload-subtext">
                            Drag and drop files here, or click to browse<br>
                            Supported formats: PDF, DOC, DOCX, TXT
                        </div>
                        <input type="file" id="fileInput" name="file" accept=".pdf,.doc,.docx,.txt" style="display: none;">
                        <button type="button" class="btn btn-secondary mt-3" onclick="document.getElementById('fileInput').click()">
                            Choose Files
                        </button>
                    </div>

                    <div class="file-info" id="fileInfo">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-alt me-2"></i>
                            <span id="fileName"></span>
                            <button type="button" class="btn btn-sm btn-outline-danger ms-auto" onclick="removeFile()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <div class="progress-container" id="progressContainer">
                        <div class="progress">
                            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                        </div>
                    </div>

                    <!-- Test Details -->
                    <div class="form-group">
                        <label class="form-label">Document Title</label>
                        <input type="text" class="form-control" name="title" placeholder="Enter a title for your document" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Test Format</label>
                        <select class="form-control" name="test_format" required>
                            <option value="">Select format</option>
                            <option value="multiple_choice">Multiple Choice</option>
                            <option value="true_false">True/False</option>
                            <option value="short_answer">Short Answer</option>
                            <option value="essay">Essay</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Total Questions</label>
                        <input type="number" class="form-control" name="total_questions" min="1" max="100" value="5" required>
                    </div>

                    <!-- Test Options -->
                    <div class="test-options">
                        <h5 class="mb-3">Additional Settings</h5>
                        
                        <div class="option-group">
                            <div class="option-label">Proctoring Options</div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="enable_camera" id="enableCamera" checked>
                                <label class="form-check-label" for="enableCamera">
                                    Enable Camera Monitoring
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="enable_screen_recording" id="enableScreen">
                                <label class="form-check-label" for="enableScreen">
                                    Enable Screen Recording
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="enable_face_detection" id="enableFace" checked>
                                <label class="form-check-label" for="enableFace">
                                    Enable Face Detection
                                </label>
                            </div>
                        </div>

                        <div class="option-group">
                            <div class="option-label">Test Timer</div>
                            <div class="time-selector">
                                <input type="number" class="form-control time-input" name="time_limit" min="5" max="300" value="30">
                                <span>minutes</span>
                            </div>
                        </div>

                        <div class="option-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="negative_marking" id="negativeMarking">
                                <label class="form-check-label" for="negativeMarking">
                                    Enable Negative Marking
                                </label>
                            </div>
                            <small class="text-muted">Turning this on will deduct marks for wrong answers</small>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-magic me-2"></i>
                        Generate Test
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            fileName.textContent = file.name;
            fileInfo.classList.add('show');
            
            // Simulate upload progress
            progressContainer.style.display = 'block';
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                progressBar.style.width = progress + '%';
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        progressContainer.style.display = 'none';
                    }, 500);
                }
            }, 100);
        }

        function removeFile() {
            fileInput.value = '';
            fileInfo.classList.remove('show');
            progressContainer.style.display = 'none';
            progressBar.style.width = '0%';
        }

        // Form submission
        document.getElementById('testForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating Test...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
