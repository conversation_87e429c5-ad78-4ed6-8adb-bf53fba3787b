__all__: list[str] = []

import cv2
import cv2.typing
import typing as _typing


# Enumerations
HAAR: int
TransformTypes = int
"""One of [HAAR]"""

BM3D_STEPALL: int
BM3D_STEP1: int
BM3D_STEP2: int
Bm3dSteps = int
"""One of [BM3D_STEPALL, BM3D_STEP1, BM3D_STEP2]"""

INPAINT_SHIFTMAP: int
INPAINT_FSR_BEST: int
INPAINT_FSR_FAST: int
InpaintTypes = int
"""One of [INPAINT_SHIFTMAP, INPAINT_FSR_BEST, INPAINT_FSR_FAST]"""



# Classes
class TonemapDurand(cv2.Tonemap):
    # Functions
    def getSaturation(self) -> float: ...

    def setSaturation(self, saturation: float) -> None: ...

    def getContrast(self) -> float: ...

    def setContrast(self, contrast: float) -> None: ...

    def getSigmaSpace(self) -> float: ...

    def setSigmaSpace(self, sigma_space: float) -> None: ...

    def getSigmaColor(self) -> float: ...

    def setSigmaColor(self, sigma_color: float) -> None: ...


class WhiteBalancer(cv2.Algorithm):
    # Functions
    @_typing.overload
    def balanceWhite(self, src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def balanceWhite(self, src: cv2.UMat, dst: cv2.UMat | None = ...) -> cv2.UMat: ...


class SimpleWB(WhiteBalancer):
    # Functions
    def getInputMin(self) -> float: ...

    def setInputMin(self, val: float) -> None: ...

    def getInputMax(self) -> float: ...

    def setInputMax(self, val: float) -> None: ...

    def getOutputMin(self) -> float: ...

    def setOutputMin(self, val: float) -> None: ...

    def getOutputMax(self) -> float: ...

    def setOutputMax(self, val: float) -> None: ...

    def getP(self) -> float: ...

    def setP(self, val: float) -> None: ...


class GrayworldWB(WhiteBalancer):
    # Functions
    def getSaturationThreshold(self) -> float: ...

    def setSaturationThreshold(self, val: float) -> None: ...


class LearningBasedWB(WhiteBalancer):
    # Functions
    @_typing.overload
    def extractSimpleFeatures(self, src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def extractSimpleFeatures(self, src: cv2.UMat, dst: cv2.UMat | None = ...) -> cv2.UMat: ...

    def getRangeMaxVal(self) -> int: ...

    def setRangeMaxVal(self, val: int) -> None: ...

    def getSaturationThreshold(self) -> float: ...

    def setSaturationThreshold(self, val: float) -> None: ...

    def getHistBinNum(self) -> int: ...

    def setHistBinNum(self, val: int) -> None: ...



# Functions
@_typing.overload
def applyChannelGains(src: cv2.typing.MatLike, gainB: float, gainG: float, gainR: float, dst: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def applyChannelGains(src: cv2.UMat, gainB: float, gainG: float, gainR: float, dst: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def bm3dDenoising(src: cv2.typing.MatLike, dstStep1: cv2.typing.MatLike, dstStep2: cv2.typing.MatLike | None = ..., h: float = ..., templateWindowSize: int = ..., searchWindowSize: int = ..., blockMatchingStep1: int = ..., blockMatchingStep2: int = ..., groupSize: int = ..., slidingStep: int = ..., beta: float = ..., normType: int = ..., step: int = ..., transformType: int = ...) -> tuple[cv2.typing.MatLike, cv2.typing.MatLike]: ...
@_typing.overload
def bm3dDenoising(src: cv2.UMat, dstStep1: cv2.UMat, dstStep2: cv2.UMat | None = ..., h: float = ..., templateWindowSize: int = ..., searchWindowSize: int = ..., blockMatchingStep1: int = ..., blockMatchingStep2: int = ..., groupSize: int = ..., slidingStep: int = ..., beta: float = ..., normType: int = ..., step: int = ..., transformType: int = ...) -> tuple[cv2.UMat, cv2.UMat]: ...
@_typing.overload
def bm3dDenoising(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., h: float = ..., templateWindowSize: int = ..., searchWindowSize: int = ..., blockMatchingStep1: int = ..., blockMatchingStep2: int = ..., groupSize: int = ..., slidingStep: int = ..., beta: float = ..., normType: int = ..., step: int = ..., transformType: int = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def bm3dDenoising(src: cv2.UMat, dst: cv2.UMat | None = ..., h: float = ..., templateWindowSize: int = ..., searchWindowSize: int = ..., blockMatchingStep1: int = ..., blockMatchingStep2: int = ..., groupSize: int = ..., slidingStep: int = ..., beta: float = ..., normType: int = ..., step: int = ..., transformType: int = ...) -> cv2.UMat: ...

def createGrayworldWB() -> GrayworldWB: ...

def createLearningBasedWB(path_to_model: str = ...) -> LearningBasedWB: ...

def createSimpleWB() -> SimpleWB: ...

def createTonemapDurand(gamma: float = ..., contrast: float = ..., saturation: float = ..., sigma_color: float = ..., sigma_space: float = ...) -> TonemapDurand: ...

def dctDenoising(src: cv2.typing.MatLike, dst: cv2.typing.MatLike, sigma: float, psize: int = ...) -> None: ...

def inpaint(src: cv2.typing.MatLike, mask: cv2.typing.MatLike, dst: cv2.typing.MatLike, algorithmType: int) -> None: ...

@_typing.overload
def oilPainting(src: cv2.typing.MatLike, size: int, dynRatio: int, code: int, dst: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def oilPainting(src: cv2.UMat, size: int, dynRatio: int, code: int, dst: cv2.UMat | None = ...) -> cv2.UMat: ...
@_typing.overload
def oilPainting(src: cv2.typing.MatLike, size: int, dynRatio: int, dst: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def oilPainting(src: cv2.UMat, size: int, dynRatio: int, dst: cv2.UMat | None = ...) -> cv2.UMat: ...


