__all__: list[str] = []

import cv2
import cv2.typing
import typing as _typing


# Enumerations
SR_FIXED: int
SR_CROSS: int
SupportRegionType = int
"""One of [SR_FIXED, SR_CROSS]"""

ST_STANDART: int
ST_BILINEAR: int
SolverType = int
"""One of [ST_STANDART, ST_BILINEAR]"""

INTERP_GEO: int
INTERP_EPIC: int
INTERP_RIC: int
InterpolationType = int
"""One of [INTERP_GEO, INTERP_EPIC, INTERP_RIC]"""

GPC_DESCRIPTOR_DCT: int
GPC_DESCRIPTOR_WHT: int
GPCDescType = int
"""One of [GPC_DESCRIPTOR_DCT, GPC_DESCRIPTOR_WHT]"""



# Classes
class DualTVL1OpticalFlow(cv2.DenseOpticalFlow):
    # Functions
    def getTau(self) -> float: ...

    def setTau(self, val: float) -> None: ...

    def getLambda(self) -> float: ...

    def setLambda(self, val: float) -> None: ...

    def getTheta(self) -> float: ...

    def setTheta(self, val: float) -> None: ...

    def getGamma(self) -> float: ...

    def setGamma(self, val: float) -> None: ...

    def getScalesNumber(self) -> int: ...

    def setScalesNumber(self, val: int) -> None: ...

    def getWarpingsNumber(self) -> int: ...

    def setWarpingsNumber(self, val: int) -> None: ...

    def getEpsilon(self) -> float: ...

    def setEpsilon(self, val: float) -> None: ...

    def getInnerIterations(self) -> int: ...

    def setInnerIterations(self, val: int) -> None: ...

    def getOuterIterations(self) -> int: ...

    def setOuterIterations(self, val: int) -> None: ...

    def getUseInitialFlow(self) -> bool: ...

    def setUseInitialFlow(self, val: bool) -> None: ...

    def getScaleStep(self) -> float: ...

    def setScaleStep(self, val: float) -> None: ...

    def getMedianFiltering(self) -> int: ...

    def setMedianFiltering(self, val: int) -> None: ...

    @classmethod
    def create(cls, tau: float = ..., lambda_: float = ..., theta: float = ..., nscales: int = ..., warps: int = ..., epsilon: float = ..., innnerIterations: int = ..., outerIterations: int = ..., scaleStep: float = ..., gamma: float = ..., medianFiltering: int = ..., useInitialFlow: bool = ...) -> DualTVL1OpticalFlow: ...


class PCAPrior:
    ...

class OpticalFlowPCAFlow(cv2.DenseOpticalFlow):
    ...

class RLOFOpticalFlowParameter:
    # Functions
    def setUseMEstimator(self, val: bool) -> None: ...

    def setSolverType(self, val: SolverType) -> None: ...

    def getSolverType(self) -> SolverType: ...

    def setSupportRegionType(self, val: SupportRegionType) -> None: ...

    def getSupportRegionType(self) -> SupportRegionType: ...

    def setNormSigma0(self, val: float) -> None: ...

    def getNormSigma0(self) -> float: ...

    def setNormSigma1(self, val: float) -> None: ...

    def getNormSigma1(self) -> float: ...

    def setSmallWinSize(self, val: int) -> None: ...

    def getSmallWinSize(self) -> int: ...

    def setLargeWinSize(self, val: int) -> None: ...

    def getLargeWinSize(self) -> int: ...

    def setCrossSegmentationThreshold(self, val: int) -> None: ...

    def getCrossSegmentationThreshold(self) -> int: ...

    def setMaxLevel(self, val: int) -> None: ...

    def getMaxLevel(self) -> int: ...

    def setUseInitialFlow(self, val: bool) -> None: ...

    def getUseInitialFlow(self) -> bool: ...

    def setUseIlluminationModel(self, val: bool) -> None: ...

    def getUseIlluminationModel(self) -> bool: ...

    def setUseGlobalMotionPrior(self, val: bool) -> None: ...

    def getUseGlobalMotionPrior(self) -> bool: ...

    def setMaxIteration(self, val: int) -> None: ...

    def getMaxIteration(self) -> int: ...

    def setMinEigenValue(self, val: float) -> None: ...

    def getMinEigenValue(self) -> float: ...

    def setGlobalMotionRansacThreshold(self, val: float) -> None: ...

    def getGlobalMotionRansacThreshold(self) -> float: ...

    @classmethod
    def create(cls) -> RLOFOpticalFlowParameter: ...


class DenseRLOFOpticalFlow(cv2.DenseOpticalFlow):
    # Functions
    def setRLOFOpticalFlowParameter(self, val: RLOFOpticalFlowParameter) -> None: ...

    def getRLOFOpticalFlowParameter(self) -> RLOFOpticalFlowParameter: ...

    def setForwardBackward(self, val: float) -> None: ...

    def getForwardBackward(self) -> float: ...

    def getGridStep(self) -> cv2.typing.Size: ...

    def setGridStep(self, val: cv2.typing.Size) -> None: ...

    def setInterpolation(self, val: InterpolationType) -> None: ...

    def getInterpolation(self) -> InterpolationType: ...

    def getEPICK(self) -> int: ...

    def setEPICK(self, val: int) -> None: ...

    def getEPICSigma(self) -> float: ...

    def setEPICSigma(self, val: float) -> None: ...

    def getEPICLambda(self) -> float: ...

    def setEPICLambda(self, val: float) -> None: ...

    def getFgsLambda(self) -> float: ...

    def setFgsLambda(self, val: float) -> None: ...

    def getFgsSigma(self) -> float: ...

    def setFgsSigma(self, val: float) -> None: ...

    def setUsePostProc(self, val: bool) -> None: ...

    def getUsePostProc(self) -> bool: ...

    def setUseVariationalRefinement(self, val: bool) -> None: ...

    def getUseVariationalRefinement(self) -> bool: ...

    def setRICSPSize(self, val: int) -> None: ...

    def getRICSPSize(self) -> int: ...

    def setRICSLICType(self, val: int) -> None: ...

    def getRICSLICType(self) -> int: ...

    @classmethod
    def create(cls, rlofParam: RLOFOpticalFlowParameter = ..., forwardBackwardThreshold: float = ..., gridStep: cv2.typing.Size = ..., interp_type: InterpolationType = ..., epicK: int = ..., epicSigma: float = ..., epicLambda: float = ..., ricSPSize: int = ..., ricSLICType: int = ..., use_post_proc: bool = ..., fgsLambda: float = ..., fgsSigma: float = ..., use_variational_refinement: bool = ...) -> DenseRLOFOpticalFlow: ...


class SparseRLOFOpticalFlow(cv2.SparseOpticalFlow):
    # Functions
    def setRLOFOpticalFlowParameter(self, val: RLOFOpticalFlowParameter) -> None: ...

    def getRLOFOpticalFlowParameter(self) -> RLOFOpticalFlowParameter: ...

    def setForwardBackward(self, val: float) -> None: ...

    def getForwardBackward(self) -> float: ...

    @classmethod
    def create(cls, rlofParam: RLOFOpticalFlowParameter = ..., forwardBackwardThreshold: float = ...) -> SparseRLOFOpticalFlow: ...


class GPCPatchDescriptor:
    ...

class GPCPatchSample:
    ...

class GPCTrainingSamples:
    ...

class GPCTree(cv2.Algorithm):
    ...

class GPCDetails:
    ...


# Functions
@_typing.overload
def calcOpticalFlowDenseRLOF(I0: cv2.typing.MatLike, I1: cv2.typing.MatLike, flow: cv2.typing.MatLike, rlofParam: RLOFOpticalFlowParameter = ..., forwardBackwardThreshold: float = ..., gridStep: cv2.typing.Size = ..., interp_type: InterpolationType = ..., epicK: int = ..., epicSigma: float = ..., epicLambda: float = ..., ricSPSize: int = ..., ricSLICType: int = ..., use_post_proc: bool = ..., fgsLambda: float = ..., fgsSigma: float = ..., use_variational_refinement: bool = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def calcOpticalFlowDenseRLOF(I0: cv2.UMat, I1: cv2.UMat, flow: cv2.UMat, rlofParam: RLOFOpticalFlowParameter = ..., forwardBackwardThreshold: float = ..., gridStep: cv2.typing.Size = ..., interp_type: InterpolationType = ..., epicK: int = ..., epicSigma: float = ..., epicLambda: float = ..., ricSPSize: int = ..., ricSLICType: int = ..., use_post_proc: bool = ..., fgsLambda: float = ..., fgsSigma: float = ..., use_variational_refinement: bool = ...) -> cv2.UMat: ...

@_typing.overload
def calcOpticalFlowSF(from_: cv2.typing.MatLike, to: cv2.typing.MatLike, layers: int, averaging_block_size: int, max_flow: int, flow: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def calcOpticalFlowSF(from_: cv2.UMat, to: cv2.UMat, layers: int, averaging_block_size: int, max_flow: int, flow: cv2.UMat | None = ...) -> cv2.UMat: ...
@_typing.overload
def calcOpticalFlowSF(from_: cv2.typing.MatLike, to: cv2.typing.MatLike, layers: int, averaging_block_size: int, max_flow: int, sigma_dist: float, sigma_color: float, postprocess_window: int, sigma_dist_fix: float, sigma_color_fix: float, occ_thr: float, upscale_averaging_radius: int, upscale_sigma_dist: float, upscale_sigma_color: float, speed_up_thr: float, flow: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def calcOpticalFlowSF(from_: cv2.UMat, to: cv2.UMat, layers: int, averaging_block_size: int, max_flow: int, sigma_dist: float, sigma_color: float, postprocess_window: int, sigma_dist_fix: float, sigma_color_fix: float, occ_thr: float, upscale_averaging_radius: int, upscale_sigma_dist: float, upscale_sigma_color: float, speed_up_thr: float, flow: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def calcOpticalFlowSparseRLOF(prevImg: cv2.typing.MatLike, nextImg: cv2.typing.MatLike, prevPts: cv2.typing.MatLike, nextPts: cv2.typing.MatLike, status: cv2.typing.MatLike | None = ..., err: cv2.typing.MatLike | None = ..., rlofParam: RLOFOpticalFlowParameter = ..., forwardBackwardThreshold: float = ...) -> tuple[cv2.typing.MatLike, cv2.typing.MatLike, cv2.typing.MatLike]: ...
@_typing.overload
def calcOpticalFlowSparseRLOF(prevImg: cv2.UMat, nextImg: cv2.UMat, prevPts: cv2.UMat, nextPts: cv2.UMat, status: cv2.UMat | None = ..., err: cv2.UMat | None = ..., rlofParam: RLOFOpticalFlowParameter = ..., forwardBackwardThreshold: float = ...) -> tuple[cv2.UMat, cv2.UMat, cv2.UMat]: ...

@_typing.overload
def calcOpticalFlowSparseToDense(from_: cv2.typing.MatLike, to: cv2.typing.MatLike, flow: cv2.typing.MatLike | None = ..., grid_step: int = ..., k: int = ..., sigma: float = ..., use_post_proc: bool = ..., fgs_lambda: float = ..., fgs_sigma: float = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def calcOpticalFlowSparseToDense(from_: cv2.UMat, to: cv2.UMat, flow: cv2.UMat | None = ..., grid_step: int = ..., k: int = ..., sigma: float = ..., use_post_proc: bool = ..., fgs_lambda: float = ..., fgs_sigma: float = ...) -> cv2.UMat: ...

def createOptFlow_DeepFlow() -> cv2.DenseOpticalFlow: ...

def createOptFlow_DenseRLOF() -> cv2.DenseOpticalFlow: ...

def createOptFlow_DualTVL1() -> DualTVL1OpticalFlow: ...

def createOptFlow_Farneback() -> cv2.DenseOpticalFlow: ...

def createOptFlow_PCAFlow() -> cv2.DenseOpticalFlow: ...

def createOptFlow_SimpleFlow() -> cv2.DenseOpticalFlow: ...

def createOptFlow_SparseRLOF() -> cv2.SparseOpticalFlow: ...

def createOptFlow_SparseToDense() -> cv2.DenseOpticalFlow: ...


