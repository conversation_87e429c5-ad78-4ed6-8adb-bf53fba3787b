# ========================================LEVEL 1===========================================
import cv2
import mediapipe as mp
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
from datetime import datetime

st.set_page_config(page_title="Advanced Proctoring System", layout="wide")

st.title("🎓 Advanced Proctoring System")
st.markdown("Tracks **attention**, **distraction**, **phone usage**, and **posture** in real-time.")

run = st.toggle("Start Proctoring")

frame_placeholder = st.empty()
status_placeholder = st.empty()

# MediaPipe setup
mp_face = mp.solutions.face_detection
mp_pose = mp.solutions.pose
mp_hands = mp.solutions.hands

face_detection = mp_face.FaceDetection(min_detection_confidence=0.5)
pose = mp_pose.Pose()
hands = mp_hands.Hands()

log_data = []

cap = cv2.VideoCapture(0)

def detect_status(frame):
    status = "Attentive"

    rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    face_results = face_detection.process(rgb)
    pose_results = pose.process(rgb)
    hand_results = hands.process(rgb)

    h, w, _ = frame.shape

    # Face detection
    face_present = face_results.detections is not None
    if not face_present:
        status = "No Face / Distraction"
        return status

    # Eye detection (rough estimation from face keypoints)
    left_eye_closed = False
    right_eye_closed = False
    if face_present:
        for detection in face_results.detections:
            for i, keypoint in enumerate(detection.location_data.relative_keypoints):
                x = int(keypoint.x * w)
                y = int(keypoint.y * h)
                cv2.circle(frame, (x, y), 2, (0, 255, 0), -1)

    # Posture detection (nose & shoulders)
    if pose_results.pose_landmarks:
        landmarks = pose_results.pose_landmarks.landmark
        nose = landmarks[mp_pose.PoseLandmark.NOSE.value]
        left_shoulder = landmarks[mp_pose.PoseLandmark.LEFT_SHOULDER.value]
        right_shoulder = landmarks[mp_pose.PoseLandmark.RIGHT_SHOULDER.value]

        shoulder_dist = abs(left_shoulder.x - right_shoulder.x)
        if shoulder_dist < 0.1:
            status = "Leaning / Bad Posture"

    # Hand detection
    if hand_results.multi_hand_landmarks:
        status = "Phone Usage / Hands Detected"

    return status

# Main loop
while run:
    ret, frame = cap.read()
    if not ret:
        break

    frame = cv2.flip(frame, 1)
    status = detect_status(frame)

    # Overlay status
    cv2.putText(frame, f'Status: {status}', (10, 30),
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255) if status != "Attentive" else (0, 255, 0), 2)

    # Log timestamp + status
    log_data.append({"Time": datetime.now().strftime("%H:%M:%S"), "Status": status})

    # Display in Streamlit
    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    frame_placeholder.image(frame_rgb, channels="RGB")
    status_placeholder.markdown(f"### 🟢 Current Status: **{status}**")

# After session ends
cap.release()

if not run and log_data:
    log_df = pd.DataFrame(log_data)

    st.subheader("📊 Proctoring Report")

    # Status Timeline
    fig = px.line(log_df, x="Time", y="Status", title="Focus Status Over Time", markers=True)
    st.plotly_chart(fig, use_container_width=True)

    # Status Summary
    st.write("🔍 **Recent Log Entries**")
    st.dataframe(log_df.tail(10))

    # Export CSV
    csv = log_df.to_csv(index=False).encode()
    st.download_button("Download Full Log as CSV", data=csv, file_name="proctoring_log.csv", mime="text/csv")


