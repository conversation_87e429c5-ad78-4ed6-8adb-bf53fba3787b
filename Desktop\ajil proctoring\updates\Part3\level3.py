# =====================================================LEVEL 3=======================================================
# With alert sound
import cv2
import mediapipe as mp
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
from datetime import datetime
import pygame
import os
from fpdf import FPDF

st.set_page_config(page_title="Advanced Proctoring System", layout="wide")

st.markdown(
    """
    <style>
    .block-container {
        background-color: #0e1117;
        color: white;
    }
    .stMarkdown h1, .stMarkdown h2, .stMarkdown h3 {
        color: white;
    }
    .stDataFrame, .stButton, .stPlotlyChart {
        background-color: #0e1117 !important;
    }
    </style>
    """,
    unsafe_allow_html=True
)

st.title("🎓 Advanced Proctoring System")
st.markdown("Tracks **attention**, **distraction**, **phone usage**, and **posture** in real-time.")

run = st.toggle("Start Proctoring")

frame_placeholder = st.empty()
status_placeholder = st.empty()
graph_placeholder = st.container()
pie_placeholder = st.container()

# MediaPipe setup
mp_face = mp.solutions.face_detection
mp_pose = mp.solutions.pose
mp_hands = mp.solutions.hands

face_detection = mp_face.FaceDetection(min_detection_confidence=0.5)
pose = mp_pose.Pose()
hands = mp_hands.Hands()

log_data = []

cap = cv2.VideoCapture(0)

# Pygame alert sound setup
pygame.mixer.init()
alert_sound = None
alert_playing = False
if os.path.exists("./assets/alert.mp3"):
    try:
        alert_sound = pygame.mixer.Sound("./assets/alert.mp3")
    except Exception as e:
        st.warning(f"⚠️ Could not load alert sound: {e}")
else:
    st.warning("⚠️ 'alert.mp3' not found in './assets'. Alerts will be silent.")

def detect_status(frame):
    status = "Attentive"
    rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    face_results = face_detection.process(rgb)
    pose_results = pose.process(rgb)
    hand_results = hands.process(rgb)
    h, w, _ = frame.shape

    # Face detection
    face_present = face_results.detections is not None
    if not face_present:
        status = "No Face / Distraction"
        return status

    # Draw face keypoints
    if face_present:
        for detection in face_results.detections:
            for keypoint in detection.location_data.relative_keypoints:
                x = int(keypoint.x * w)
                y = int(keypoint.y * h)
                cv2.circle(frame, (x, y), 2, (0, 255, 0), -1)

    # Posture detection
    if pose_results.pose_landmarks:
        landmarks = pose_results.pose_landmarks.landmark
        left_shoulder = landmarks[mp_pose.PoseLandmark.LEFT_SHOULDER.value]
        right_shoulder = landmarks[mp_pose.PoseLandmark.RIGHT_SHOULDER.value]
        shoulder_dist = abs(left_shoulder.x - right_shoulder.x)
        if shoulder_dist < 0.1:
            status = "Leaning / Bad Posture"

    # Hand detection
    if hand_results.multi_hand_landmarks:
        status = "Phone Usage / Hands Detected"

    return status

# Main loop
while run:
    ret, frame = cap.read()
    if not ret:
        break

    frame = cv2.flip(frame, 1)
    status = detect_status(frame)

    # Play/Stop alert sound based on status
    if status != "Attentive":
        if alert_sound and not alert_playing:
            alert_sound.play(-1)
            alert_playing = True
    else:
        if alert_sound and alert_playing:
            alert_sound.stop()
            alert_playing = False

    # Overlay status
    cv2.putText(frame, f'Status: {status}', (10, 30),
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255) if status != "Attentive" else (0, 255, 0), 2)

    # Log timestamp + status
    log_data.append({"Time": datetime.now().strftime("%H:%M:%S"), "Status": status})

    # Display in Streamlit
    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    frame_placeholder.image(frame_rgb, channels="RGB")
    status_placeholder.markdown(f"### 🟢 Current Status: **{status}**")

cap.release()

if not run and log_data:
    log_df = pd.DataFrame(log_data)

    st.subheader("📊 Proctoring Report")

    # Status line chart
    graph_placeholder.markdown("### 🔁 Focus Status Over Time")
    fig = px.line(
        log_df,
        x="Time",
        y="Status",
        title="Focus Timeline",
        line_shape="linear",
        markers=False,
        template="plotly_dark",
        color_discrete_sequence=["cyan"]
    )
    fig.update_layout(
        height=400,
        margin=dict(l=10, r=10, t=50, b=40),
        plot_bgcolor='#0e1117',
        paper_bgcolor='#0e1117',
        font=dict(color="white")
    )
    graph_placeholder.plotly_chart(fig, use_container_width=True)

    # Percentage pie chart
    st.markdown("### 📈 Attention Distribution")
    status_counts = log_df["Status"].value_counts()
    pie_fig = px.pie(
        names=status_counts.index,
        values=status_counts.values,
        title="Status Distribution",
        template="plotly_dark",
        color_discrete_sequence=px.colors.sequential.RdBu
    )
    pie_fig.update_layout(
        height=400,
        plot_bgcolor='#0e1117',
        paper_bgcolor='#0e1117',
        font=dict(color="white")
    )
    pie_placeholder.plotly_chart(pie_fig, use_container_width=True)

    # Data table
    st.markdown("### 🧾 Recent Activity Log")
    st.dataframe(log_df.tail(10))

    # CSV Export
    csv = log_df.to_csv(index=False).encode()
    st.download_button("⬇️ Download Log as CSV", data=csv, file_name="proctoring_log.csv", mime="text/csv")

    # PDF Export
    def generate_pdf(df):
        pdf = FPDF()
        pdf.add_page()
        pdf.set_font("Arial", size=12)
        pdf.cell(200, 10, txt="Proctoring Report", ln=True, align="C")

        for i, row in df.iterrows():
            pdf.cell(200, 10, txt=f"{row['Time']} - {row['Status']}", ln=True)

        path = "proctoring_report.pdf"
        pdf.output(path)
        return path

    if st.button("📤 Export PDF Report"):
        pdf_path = generate_pdf(log_df)
        with open(pdf_path, "rb") as f:
            st.download_button("Download PDF", data=f, file_name="proctoring_report.pdf", mime="application/pdf")

