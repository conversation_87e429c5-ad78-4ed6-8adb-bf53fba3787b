# =============================================LEVEL 4===========================================
# Advanced Proctoring System - Fully Featured Version with ⏱️ Session Duration
import cv2
import mediapipe as mp
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
from datetime import datetime
import pygame
import os
from fpdf import FPDF
import time

# App UI setup
st.set_page_config(page_title="Advanced Proctoring System", layout="wide")
st.markdown("""
    <style>
    .block-container {
        background-color: #0e1117;
        color: white;
    }
    .stMarkdown h1, .stMarkdown h2, .stMarkdown h3 {
        color: white;
    }
    .stDataFrame, .stButton, .stPlotlyChart {
        background-color: #0e1117 !important;
    }
    </style>
""", unsafe_allow_html=True)

st.title("🎓 Advanced Proctoring System")
st.markdown("Tracks **attention**, **distraction**, **phone usage**, and **posture** in real-time.")

run = st.toggle("▶️ Start Proctoring")

frame_placeholder = st.empty()
status_placeholder = st.empty()
banner_placeholder = st.empty()
graph_placeholder = st.container()
pie_placeholder = st.container()
session_timer_placeholder = st.empty()

# MediaPipe setup
mp_face = mp.solutions.face_detection
mp_pose = mp.solutions.pose
mp_hands = mp.solutions.hands
face_detection = mp_face.FaceDetection(min_detection_confidence=0.5)
pose = mp_pose.Pose()
hands = mp_hands.Hands()

# Initialize pygame mixer
pygame.mixer.init()
alert_sound = None
if os.path.exists("./assets/alert.mp3"):
    try:
        alert_sound = pygame.mixer.Sound("./assets/alert.mp3")
    except Exception as e:
        st.warning(f"⚠️ Could not load alert sound: {e}")
else:
    st.warning("⚠️ 'alert.mp3' not found in assets folder.")

# Logging and counters
log_data = []
screenshot_count = 0
start_time = time.time()
last_played = False  # Track alert sound status
cap = cv2.VideoCapture(0)

# Status detection
def detect_status(frame):
    status = "Attentive"
    rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    face_results = face_detection.process(rgb)
    pose_results = pose.process(rgb)
    hand_results = hands.process(rgb)
    h, w, _ = frame.shape

    face_present = face_results.detections is not None
    if not face_present:
        status = "No Face / Distraction"
        return status

    if pose_results.pose_landmarks:
        landmarks = pose_results.pose_landmarks.landmark
        left_shoulder = landmarks[mp_pose.PoseLandmark.LEFT_SHOULDER.value]
        right_shoulder = landmarks[mp_pose.PoseLandmark.RIGHT_SHOULDER.value]
        shoulder_dist = abs(left_shoulder.x - right_shoulder.x)
        if shoulder_dist < 0.1:
            status = "Leaning / Bad Posture"

    if hand_results.multi_hand_landmarks:
        status = "Phone Usage / Hands Detected"

    return status

# Main loop
while run:
    ret, frame = cap.read()
    if not ret:
        break

    frame = cv2.flip(frame, 1)
    status = detect_status(frame)

    # Banner message
    if status != "Attentive":
        banner_placeholder.markdown(f"## 🚨 **Distraction Detected: {status}**")
    else:
        banner_placeholder.empty()

    # Alert sound management
    if status != "Attentive":
        if not last_played and alert_sound:
            alert_sound.play(-1)
            last_played = True
    elif last_played:
        if alert_sound:
            alert_sound.stop()
            last_played = False

    # Overlay status
    cv2.putText(frame, f'Status: {status}', (10, 30),
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255) if status != "Attentive" else (0, 255, 0), 2)

    # Screenshot on distraction
    if status != "Attentive":
        screenshot_path = f"screenshot_{screenshot_count}.jpg"
        cv2.imwrite(screenshot_path, frame)
        screenshot_count += 1

    # Log data
    log_data.append({"Time": datetime.now().strftime("%H:%M:%S"), "Status": status})

    # Streamlit display
    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    frame_placeholder.image(frame_rgb, channels="RGB")
    status_placeholder.markdown(f"### 🟢 Current Status: **{status}**")
    elapsed_time = int(time.time() - start_time)
    session_timer_placeholder.markdown(f"⏱️ Session Duration: **{elapsed_time} seconds**")

cap.release()

# After session
if not run and log_data:
    log_df = pd.DataFrame(log_data)
    st.subheader("📊 Proctoring Report")

    graph_placeholder.markdown("### 🔁 Focus Status Over Time")
    fig = px.line(
        log_df,
        x="Time",
        y="Status",
        title="Focus Timeline",
        line_shape="linear",
        markers=False,
        template="plotly_dark",
        color_discrete_sequence=["cyan"]
    )
    fig.update_layout(
        height=400,
        margin=dict(l=10, r=10, t=50, b=40),
        plot_bgcolor='#0e1117',
        paper_bgcolor='#0e1117',
        font=dict(color="white")
    )
    graph_placeholder.plotly_chart(fig, use_container_width=True)

    st.markdown("### 📈 Attention Distribution")
    status_counts = log_df["Status"].value_counts()
    pie_fig = px.pie(
        names=status_counts.index,
        values=status_counts.values,
        title="Status Distribution",
        template="plotly_dark",
        color_discrete_sequence=px.colors.sequential.RdBu
    )
    pie_fig.update_layout(
        height=400,
        plot_bgcolor='#0e1117',
        paper_bgcolor='#0e1117',
        font=dict(color="white")
    )
    pie_placeholder.plotly_chart(pie_fig, use_container_width=True)

    st.markdown("### 🧾 Recent Activity Log")
    st.dataframe(log_df.tail(10))

    # Attention score
    attentive_count = log_df[log_df['Status'] == 'Attentive'].shape[0]
    attention_score = round((attentive_count / len(log_df)) * 100)
    st.markdown(f"### ⭐ **Attention Score: {attention_score}%**")

    if attention_score >= 90:
        st.success("Excellent Focus ⭐⭐⭐")
    elif attention_score >= 75:
        st.info("Good Focus ⭐⭐")
    elif attention_score >= 50:
        st.warning("Moderate Focus ⭐")
    else:
        st.error("Low Focus ❌")

    # CSV download
    csv = log_df.to_csv(index=False).encode()
    st.download_button("⬇️ Download Log as CSV", data=csv, file_name="proctoring_log.csv", mime="text/csv")

    # PDF report
    def generate_pdf(df):
        pdf = FPDF()
        pdf.add_page()
        pdf.set_font("Arial", size=12)
        pdf.cell(200, 10, txt="Proctoring Report", ln=True, align="C")
        for i, row in df.iterrows():
            pdf.cell(200, 10, txt=f"{row['Time']} - {row['Status']}", ln=True)
        path = "proctoring_report.pdf"
        pdf.output(path)
        return path

    if st.button("📤 Export PDF Report"):
        pdf_path = generate_pdf(log_df)
        with open(pdf_path, "rb") as f:
            st.download_button("Download PDF", data=f, file_name="proctoring_report.pdf", mime="application/pdf")















