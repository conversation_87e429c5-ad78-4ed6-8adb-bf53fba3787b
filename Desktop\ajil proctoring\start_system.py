"""
Start Proctoring System
Main entry point with original UI styling
"""
import streamlit as st
import hashlib
from simple_models import get_db_session, User, Base, engine

def main():
    """Main application with original UI"""
    st.set_page_config(
        page_title="Proctoring System",
        page_icon="🎯",
        layout="wide",
        initial_sidebar_state="collapsed"
    )
    
    # Initialize system
    initialize_system()
    
    # Enhanced CSS for better visibility
    st.markdown("""
    <style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 2rem;
    }

    .role-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        margin: 1rem;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .role-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    }

    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin: 2rem 0;
    }

    .feature-card {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        color: #333;
    }

    .stButton > button {
        width: 100%;
        height: 60px;
        font-size: 1.1rem;
        font-weight: bold;
    }

    /* Fix white text visibility issues */
    .stMarkdown, .stText {
        color: #333 !important;
    }

    .stForm {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border: 1px solid #dee2e6;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Check if role is selected
    if 'selected_role' in st.session_state:
        if st.session_state.selected_role == 'admin':
            show_admin_interface()
        elif st.session_state.selected_role == 'student':
            show_student_interface()
        return
    
    # Main header
    st.markdown("""
    <div class="main-header">
        <h1>🎯 AI Examiner</h1>
        <p style="font-size: 1.3rem;">Advanced AI-Powered Online Examination System</p>
        <p style="font-size: 1rem; opacity: 0.9;">Secure • Intelligent • Comprehensive</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Role selection
    st.markdown("### 🚀 Select Your Role to Continue")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        col_admin, col_student = st.columns(2)
        
        with col_admin:
            if st.button("🔧 Administrator\n\nCreate tests, manage sessions, grade results",
                        type="primary", help="Access administrative features"):
                st.session_state.selected_role = 'admin'
                st.rerun()

        with col_student:
            if st.button("📝 Student Portal\n\nJoin sessions, take tests, view results",
                        type="secondary", help="Access student features"):
                st.session_state.selected_role = 'student'
                st.rerun()
    
    # System overview
    st.markdown("---")
    st.markdown("### 🎯 System Overview")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        **🔧 For Administrators:**
        - 📄 Upload PDF documents to create tests
        - ⏱️ Set session duration (5 minutes to 24 hours)
        - 👥 Manage participant limits
        - 🎥 Access audio/video recordings
        - 📊 Grade submissions with remarks
        - 🎮 Real-time session control
        """)
    
    with col2:
        st.markdown("""
        **📝 For Students:**
        - 🔐 Secure login with credentials
        - 📋 View assigned test sessions
        - 🎯 Take tests with real-time proctoring
        - 🎥 Automatic audio/video recording
        - ⏰ Live countdown timers
        - 📊 Instant results and feedback
        """)
    
    with col3:
        st.markdown("""
        **🎯 Key Features:**
        - 🤖 AI-powered question generation from PDFs
        - 🎥 Comprehensive audio/video recording
        - ⚡ Automatic grading with manual override
        - 🕐 Flexible session time limits
        - 🔒 Secure session management
        - 📈 Detailed progress tracking
        """)
    
    # Quick start guide
    st.markdown("---")
    st.markdown("### 🚀 Quick Start Guide")
    
    with st.expander("📋 How to Get Started", expanded=False):
        st.markdown("""
        **For Administrators:**
        1. Click "Admin Dashboard" above
        2. Login with: <EMAIL> / admin123
        3. Go to "Create Test" tab
        4. Upload a PDF document
        5. Set test title and duration
        6. Go to "Manage Sessions" tab
        7. Create a new session with time limits
        8. Share session details with students
        
        **For Students:**
        1. Click "Student Portal" above
        2. Login with: <EMAIL> / student123
        3. View available test sessions
        4. Click "Start Test" to begin
        5. Complete test with audio/video recording
        6. View results immediately after submission
        
        **System Requirements:**
        - Modern web browser with camera/microphone access
        - Stable internet connection
        - Quiet, well-lit environment for testing
        """)
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 1rem;">
        <p>🎯 AI Examiner | Advanced AI-Powered Online Examination System</p>
        <p>Secure • Intelligent • Comprehensive</p>
    </div>
    """, unsafe_allow_html=True)

def initialize_system():
    """Initialize database and create default users"""
    if 'system_initialized' not in st.session_state:
        # Create tables
        Base.metadata.create_all(engine)
        
        # Create default users
        db = get_db_session()
        try:
            # Admin user
            admin = db.query(User).filter(User.email == "<EMAIL>").first()
            if not admin:
                admin_password = hashlib.sha256("admin123".encode()).hexdigest()
                admin = User(
                    name="Admin User",
                    email="<EMAIL>",
                    password_hash=admin_password,
                    role="admin",
                    is_active=True
                )
                db.add(admin)
            
            # Student user
            student = db.query(User).filter(User.email == "<EMAIL>").first()
            if not student:
                student_password = hashlib.sha256("student123".encode()).hexdigest()
                student = User(
                    name="Test Student",
                    email="<EMAIL>",
                    password_hash=student_password,
                    role="student",
                    is_active=True
                )
                db.add(student)
            
            db.commit()
        except:
            db.rollback()
        finally:
            db.close()
        
        st.session_state.system_initialized = True

def show_admin_interface():
    """Show admin interface"""
    from simple_admin import show_simple_admin
    show_simple_admin()

def show_student_interface():
    """Show student interface"""
    from simple_student import show_simple_student
    show_simple_student()

if __name__ == "__main__":
    main()
