__all__: list[str] = []

import cv2
import cv2.typing
import typing as _typing


# Classes
class Feature:
    x: int
    y: int
    label: int

    # Functions
    @_typing.overload
    def __init__(self) -> None: ...
    @_typing.overload
    def __init__(self, x: int, y: int, label: int) -> None: ...


class Template:
    @property
    def width(self) -> int: ...
    @property
    def height(self) -> int: ...
    @property
    def pyramid_level(self) -> int: ...
    @property
    def features(self) -> _typing.Sequence[Feature]: ...

class QuantizedPyramid:
    # Functions
    def quantize(self, dst: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...

    def extractTemplate(self) -> tuple[bool, Template]: ...

    def pyrDown(self) -> None: ...


class Modality:
    # Functions
    def process(self, src: cv2.typing.MatLike, mask: cv2.typing.MatLike | None = ...) -> QuantizedPyramid: ...

    def name(self) -> str: ...

    def read(self, fn: cv2.FileNode) -> None: ...

    @classmethod
    @_typing.overload
    def create(cls, modality_type: str) -> Modality: ...
    @classmethod
    @_typing.overload
    def create(cls, fn: cv2.FileNode) -> Modality: ...


class ColorGradient(Modality):
    @property
    def weak_threshold(self) -> float: ...
    @property
    def num_features(self) -> int: ...
    @property
    def strong_threshold(self) -> float: ...

    # Functions
    @classmethod
    def create(cls, weak_threshold: float, num_features: int, strong_threshold: float) -> ColorGradient: ...


class DepthNormal(Modality):
    @property
    def distance_threshold(self) -> int: ...
    @property
    def difference_threshold(self) -> int: ...
    @property
    def num_features(self) -> int: ...
    @property
    def extract_threshold(self) -> int: ...

    # Functions
    @classmethod
    def create(cls, distance_threshold: int, difference_threshold: int, num_features: int, extract_threshold: int) -> DepthNormal: ...


class Match:
    x: int
    y: int
    similarity: float
    class_id: str
    template_id: int

    # Functions
    @_typing.overload
    def __init__(self) -> None: ...
    @_typing.overload
    def __init__(self, x: int, y: int, similarity: float, class_id: str, template_id: int) -> None: ...


class Detector:
    # Functions
    @_typing.overload
    def __init__(self) -> None: ...
    @_typing.overload
    def __init__(self, modalities: _typing.Sequence[Modality], T_pyramid: _typing.Sequence[int]) -> None: ...

    @_typing.overload
    def match(self, sources: _typing.Sequence[cv2.typing.MatLike], threshold: float, class_ids: _typing.Sequence[str] = ..., quantized_images: _typing.Sequence[cv2.typing.MatLike] | None = ..., masks: _typing.Sequence[cv2.typing.MatLike] | None = ...) -> tuple[_typing.Sequence[Match], _typing.Sequence[cv2.typing.MatLike]]: ...
    @_typing.overload
    def match(self, sources: _typing.Sequence[cv2.typing.MatLike], threshold: float, class_ids: _typing.Sequence[str] = ..., quantized_images: _typing.Sequence[cv2.UMat] | None = ..., masks: _typing.Sequence[cv2.typing.MatLike] | None = ...) -> tuple[_typing.Sequence[Match], _typing.Sequence[cv2.UMat]]: ...

    def addTemplate(self, sources: _typing.Sequence[cv2.typing.MatLike], class_id: str, object_mask: cv2.typing.MatLike) -> tuple[int, cv2.typing.Rect]: ...

    def addSyntheticTemplate(self, templates: _typing.Sequence[Template], class_id: str) -> int: ...

    def getModalities(self) -> _typing.Sequence[Modality]: ...

    def getT(self, pyramid_level: int) -> int: ...

    def pyramidLevels(self) -> int: ...

    def getTemplates(self, class_id: str, template_id: int) -> _typing.Sequence[Template]: ...

    @_typing.overload
    def numTemplates(self) -> int: ...
    @_typing.overload
    def numTemplates(self, class_id: str) -> int: ...

    def numClasses(self) -> int: ...

    def classIds(self) -> _typing.Sequence[str]: ...

    def read(self, fn: cv2.FileNode) -> None: ...

    def readClasses(self, class_ids: _typing.Sequence[str], format: str = ...) -> None: ...

    def writeClasses(self, format: str = ...) -> None: ...



# Functions
def colormap(quantized: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...

@_typing.overload
def drawFeatures(img: cv2.typing.MatLike, templates: _typing.Sequence[Template], tl: cv2.typing.Point2i, size: int = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def drawFeatures(img: cv2.UMat, templates: _typing.Sequence[Template], tl: cv2.typing.Point2i, size: int = ...) -> cv2.UMat: ...

def getDefaultLINE() -> Detector: ...

def getDefaultLINEMOD() -> Detector: ...


